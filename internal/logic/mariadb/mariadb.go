package mariadb

import (
	"dataSyncHub/internal/consts"
	"dataSyncHub/internal/model"
	"dataSyncHub/internal/model/ams"
	"dataSyncHub/internal/service"
	"dataSyncHub/utility"
	"fmt"
	"strings"

	"github.com/gogf/gf/v2/container/garray"
	"github.com/gogf/gf/v2/container/gset"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	amqp "github.com/rabbitmq/amqp091-go"
	"golang.org/x/net/context"
)

func init() {
	service.RegisterMariaDB(New())
}

type sMariaDB struct {
	sqlValidator   *SQLValidator
	securityConfig *SecurityConfig
}

func New() service.IMariaDB {
	return &sMariaDB{
		sqlValidator:   NewSQLValidator(),
		securityConfig: GetDefaultSecurityConfig(),
	}
}
func (s *sMariaDB) logger() glog.ILogger {
	return g.Log().Cat(consts.CatDB)
}

// isSchemaExistsInCache 檢查 schema 是否在緩存中
func (s *sMariaDB) isSchemaExistsInCache(ctx context.Context, schema string) (exists bool, fromCache bool) {
	result, err := g.Redis().SIsMember(ctx, consts.CacheKeySchemas, schema)
	if err != nil {
		s.logger().Warningf(ctx, "Redis cache error for schema check, fallback to database: %v", err)
		return false, false // 降級到數據庫查詢
	}
	return result == 1, true
}

// addSchemaToCache 添加 schema 到緩存
func (s *sMariaDB) addSchemaToCache(ctx context.Context, schema string) error {
	_, err := g.Redis().SAdd(ctx, consts.CacheKeySchemas, schema)
	if err != nil {
		s.logger().Warningf(ctx, "Failed to add schema to cache: %v", err)
		return err
	}
	// 設置過期時間
	_, expireErr := g.Redis().Expire(ctx, consts.CacheKeySchemas, consts.CacheExpireSchema)
	if expireErr != nil {
		s.logger().Warningf(ctx, "Failed to set expire time for schema cache: %v", expireErr)
	}
	return nil
}

// isTableExistsInCache 檢查 table 是否在緩存中
func (s *sMariaDB) isTableExistsInCache(ctx context.Context, schema, table string) (exists bool, fromCache bool) {
	key := fmt.Sprintf(consts.CacheKeyTables, schema)
	result, err := g.Redis().SIsMember(ctx, key, table)
	if err != nil {
		s.logger().Warningf(ctx, "Redis cache error for table check, fallback to database: %v", err)
		return false, false
	}
	return result == 1, true
}

// addTableToCache 添加 table 到緩存
func (s *sMariaDB) addTableToCache(ctx context.Context, schema, table string) error {
	key := fmt.Sprintf(consts.CacheKeyTables, schema)
	_, err := g.Redis().SAdd(ctx, key, table)
	if err != nil {
		s.logger().Warningf(ctx, "Failed to add table to cache: %v", err)
		return err
	}
	// 設置過期時間
	_, expireErr := g.Redis().Expire(ctx, key, consts.CacheExpireTable)
	if expireErr != nil {
		s.logger().Warningf(ctx, "Failed to set expire time for table cache: %v", expireErr)
	}
	return nil
}

// clearSchemaCache 清理所有 schema 緩存
func (s *sMariaDB) clearSchemaCache(ctx context.Context) error {
	_, err := g.Redis().Del(ctx, consts.CacheKeySchemas)
	if err != nil {
		s.logger().Warningf(ctx, "Failed to clear schema cache: %v", err)
	}
	return err
}

// clearTableCache 清理指定 schema 的 table 緩存
func (s *sMariaDB) clearTableCache(ctx context.Context, schema string) error {
	key := fmt.Sprintf(consts.CacheKeyTables, schema)
	_, err := g.Redis().Del(ctx, key)
	if err != nil {
		s.logger().Warningf(ctx, "Failed to clear table cache for schema %s: %v", schema, err)
	}
	return err
}

// getCacheStats 獲取緩存統計信息
func (s *sMariaDB) getCacheStats(ctx context.Context) map[string]interface{} {
	stats := make(map[string]interface{})

	// Schema 緩存統計
	schemaCount, err := g.Redis().SCard(ctx, consts.CacheKeySchemas)
	if err != nil {
		s.logger().Warningf(ctx, "Failed to get schema cache stats: %v", err)
		stats["schema_count"] = 0
	} else {
		stats["schema_count"] = int(schemaCount)
	}

	// Table 緩存統計
	schemas, err := g.Redis().SMembers(ctx, consts.CacheKeySchemas)
	if err != nil {
		s.logger().Warningf(ctx, "Failed to get schemas for table stats: %v", err)
		stats["table_count"] = 0
	} else {
		totalTables := 0
		for _, schema := range schemas.Strings() {
			key := fmt.Sprintf(consts.CacheKeyTables, schema)
			count, err := g.Redis().SCard(ctx, key)
			if err == nil {
				totalTables += int(count)
			}
		}
		stats["table_count"] = totalTables
	}

	return stats
}

func (s *sMariaDB) createSchemaIfNotExists(ctx context.Context, schema string) (err error) {
	// 1. 先檢查緩存
	if exists, fromCache := s.isSchemaExistsInCache(ctx, schema); fromCache && exists {
		s.logger().Debugf(ctx, "Schema %s exists in cache, skip creation", schema)
		return nil
	}

	// 2. 執行數據庫創建（保持原有邏輯）
	_, err = g.DB().Exec(ctx, fmt.Sprintf("CREATE DATABASE IF NOT EXISTS `%s` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci", schema))
	if err != nil {
		s.logger().Errorf(ctx, "Failed to create schema %s: %v", schema, err)
		return err
	}

	// 3. 更新緩存
	if cacheErr := s.addSchemaToCache(ctx, schema); cacheErr != nil {
		// 緩存更新失敗不影響主要功能，只記錄日誌
		s.logger().Warningf(ctx, "Failed to update schema cache for %s: %v", schema, cacheErr)
	}

	s.logger().Debugf(ctx, "Schema %s created and cached successfully", schema)
	return nil
}

func (s *sMariaDB) ExecuteSQL(ctx context.Context, in model.ExecuteSQLInput) (out *model.ExecuteSQLOutput, err error) {
	// 記錄請求（脫敏處理）
	sanitizedInput := s.sanitizeLogInput(in)
	s.logger().Infof(ctx, "ExecuteSQL request: %v", sanitizedInput)

	out = &model.ExecuteSQLOutput{
		Success: true,
		Message: "success",
	}

	// 1. 基本參數驗證
	if err = g.Validator().Data(in).Run(ctx); err != nil {
		out.Success = false
		out.Message = err.Error()
		return
	}

	// 2. SQL 安全驗證
	if s.securityConfig.SQL.Enabled {
		clientIP := s.getClientIP(ctx)
		config := s.securityConfig.ToSQLValidationConfig()
		if err = s.sqlValidator.ValidateSQL(ctx, in.RawSQL, config); err != nil {
			// 記錄安全事件
			s.sqlValidator.LogSecurityEvent(ctx, "SQL_VALIDATION_FAILED", in.RawSQL, clientIP)
			out.Success = false
			out.Message = "SQL statement security validation failed: " + err.Error()
			return
		}

		// 記錄敏感操作
		if s.securityConfig.Audit.LogSensitiveOperations && s.securityConfig.IsSensitiveOperation(in.RawSQL) {
			s.logger().Warningf(ctx, "Sensitive SQL operation: %s, Client IP: %s", s.extractSQLType(in.RawSQL), clientIP)
		}
	}

	// 3. 確保數據庫 Schema 存在
	if err = s.createSchemaIfNotExists(ctx, in.Schema); err != nil {
		out.Success = false
		out.Message = err.Error()
		return
	}

	// 4. 執行 SQL（已通過安全驗證）
	if _, err = g.DB().Schema(in.Schema).Exec(ctx, in.RawSQL); err != nil {
		s.logger().Errorf(ctx, "SQL execution failed: %v, SQL: %s", err, in.RawSQL)
		out.Success = false
		out.Message = err.Error()
		return
	}

	s.logger().Infof(ctx, "SQL execution successful, Schema: %s", in.Schema)
	return
}

func (s *sMariaDB) BatchExecuteSQL(ctx context.Context, in model.BatchExecuteSQLInput) (out *model.BatchExecuteSQLOutput, err error) {
	// 記錄請求（脫敏處理）
	sanitizedInput := s.sanitizeBatchLogInput(in)
	s.logger().Infof(ctx, "BatchExecuteSQL request: %v", sanitizedInput)

	out = &model.BatchExecuteSQLOutput{
		Success:    true,
		TotalCount: len(in.SQLList),
	}

	// 1. 批量 SQL 安全驗證
	if s.securityConfig.SQL.Enabled {
		clientIP := s.getClientIP(ctx)
		config := s.securityConfig.ToSQLValidationConfig()
		if err = s.sqlValidator.ValidateBatchSQL(ctx, in.SQLList, config); err != nil {
			// 記錄安全事件
			s.sqlValidator.LogSecurityEvent(ctx, "BATCH_SQL_VALIDATION_FAILED",
				fmt.Sprintf("Batch SQL validation failed, %d statements total", len(in.SQLList)), clientIP)
			out.Success = false
			out.Errors = append(out.Errors, "Batch SQL security validation failed: "+err.Error())
			return
		}

		// 檢查敏感操作
		if s.securityConfig.Audit.LogSensitiveOperations {
			for i, sql := range in.SQLList {
				if s.securityConfig.IsSensitiveOperation(sql) {
					s.logger().Warningf(ctx, "Sensitive operation in batch execution [%d]: %s, Client IP: %s",
						i+1, s.extractSQLType(sql), clientIP)
				}
			}
		}
	}

	// 2. 確保數據庫 Schema 存在
	if err = s.createSchemaIfNotExists(ctx, in.Schema); err != nil {
		out.Success = false
		out.Errors = append(out.Errors, err.Error())
		return
	}

	// 3. 批量執行 SQL（已通過安全驗證）
	db := g.DB().Schema(in.Schema)
	for i, sql := range in.SQLList {
		if _, e := db.Exec(ctx, sql); e != nil {
			// 檢查是否為可忽略的錯誤（如重複記錄）
			if gstr.ContainsI(e.Error(), "Duplicate") ||
				gstr.ContainsI(e.Error(), "already exists") {
				out.SuccessCount++
				s.logger().Debugf(ctx, "Ignoring duplicate record error, SQL #%d", i+1)
				continue
			}

			out.FailCount++
			out.Success = false
			errorMsg := fmt.Sprintf("SQL #%d execution failed: %v", i+1, e)
			out.Errors = append(out.Errors, errorMsg)
			s.logger().Errorf(ctx, "Batch execution failed: %s, SQL: %s", errorMsg, sql)
		} else {
			out.SuccessCount++
		}
	}

	out.Success = out.FailCount == 0
	s.logger().Infof(ctx, "Batch SQL execution completed, success: %d, failed: %d", out.SuccessCount, out.FailCount)
	return
}
func (s *sMariaDB) OnMessage(ctx context.Context, message any) {
	var msg *amqp.Delivery
	_ = gconv.Struct(message, &msg)

	if msg != nil {
		bodyStr := gjson.New(msg.Body).MustToJsonIndentString()
		convertedJSON, e := utility.ProcessVectorJSON(bodyStr)
		if e == nil {
			bodyStr = convertedJSON
		}

		s.logger().Debugf(ctx, "RouteKey:%q, type=%v body=%v", msg.RoutingKey, msg.Type, bodyStr)
		// check the database
		jsBody := gjson.New(msg.Body)
		if jsBody.Contains("schema") {
			_ = s.createSchemaIfNotExists(ctx, jsBody.Get("schema").String())
		}

		switch msg.Type {
		case consts.ActionInsert:
			if err := s.actionInsertData(ctx, msg); err != nil {
				s.logger().Error(ctx, err)
				return
			}

		case consts.ActionUpdate:
			if err := s.actionUpdate(ctx, msg); err != nil {
				s.logger().Error(ctx, err)
				return
			}
		case consts.ActionDelete:
			if err := s.actionDelete(ctx, msg); err != nil {
				s.logger().Error(ctx, err)
				return
			}
		case consts.ActionUpdateOrInsert:
			if err := s.actionInsertOrUpdate(ctx, msg); err != nil {
				s.logger().Error(ctx, err)
				return
			}
		case consts.ActionCreateSchema:
			if err := s.actionCreateSchema(ctx, msg); err != nil {
				s.logger().Error(ctx, err)
				return
			}

		case consts.ActionCreateTable:
			if err := s.actionCreateTable(ctx, msg); err != nil {
				s.logger().Error(ctx, err)
				return
			}

		}

	} else {
		s.logger().Debug(ctx, "the message is nil")
	}
}
func (s *sMariaDB) GetContents(ctx context.Context, in model.GetContentsReq) (out *model.GetContentsRes, err error) {
	// 記錄請求（脫敏處理）
	sanitizedInput := s.sanitizeGetContentsLogInput(in)
	s.logger().Infof(ctx, "GetContents request: %v", sanitizedInput)

	if g.IsEmpty(in.Schema) {
		err = gerror.New("schema is empty")
		return
	}

	out = &model.GetContentsRes{}
	db := g.DB().Schema(in.Schema)
	fnMakeResult := func(r gdb.Result) {
		out.Contents = make([]map[string]any, len(r))
		for i, record := range r {
			out.Contents[i] = make(map[string]any)
			for field, value := range record {
				out.Contents[i][field] = value
			}
		}
	}

	// 如果提供了原始 SQL，需要進行安全驗證
	if !g.IsEmpty(in.RawSQL) {
		// SQL 安全驗證（查詢操作使用更嚴格的配置）
		if s.securityConfig.SQL.Enabled {
			clientIP := s.getClientIP(ctx)
			queryConfig := GetQueryOnlyConfig().ToSQLValidationConfig()

			if err = s.sqlValidator.ValidateSQL(ctx, in.RawSQL, queryConfig); err != nil {
				// 記錄安全事件
				s.sqlValidator.LogSecurityEvent(ctx, "QUERY_SQL_VALIDATION_FAILED", in.RawSQL, clientIP)
				return nil, gerror.New("Query SQL security validation failed: " + err.Error())
			}
		}

		if r, e := db.GetAll(ctx, in.RawSQL); e != nil {
			// 檢查是否為表不存在的錯誤
			if gstr.ContainsI(e.Error(), "doesn't exist") ||
				(gstr.ContainsI(e.Error(), "Table") && gstr.ContainsI(e.Error(), "doesn't exist")) {
				s.logger().Warningf(ctx, "Table does not exist in SQL: %s, Error: %v", in.RawSQL, e)
				return nil, gerror.NewCode(consts.ErrTableNotExists, "table does not exist")
			}
			s.logger().Errorf(ctx, "Query SQL execution failed: %v, SQL: %s", e, in.RawSQL)
			return nil, gerror.NewCode(consts.ErrDatabaseError, e.Error())
		} else {
			fnMakeResult(r)
			s.logger().Infof(ctx, "Query SQL execution successful, returned %d records", len(out.Contents))
			return
		}
	}
	if g.IsEmpty(in.Table) {
		return nil, gerror.New("table is empty")
	}

	if err = db.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		m := tx.Model(in.Table)
		if !g.IsEmpty(in.WhereCond) {
			m.Wheref(in.WhereCond, in.Params...)
		}
		if in.Limit > 0 {
			m.Limit(in.Limit)
		}
		if len(in.Fields) > 0 {
			m.Fields(in.Fields)
		}
		if !g.IsEmpty(gstr.Trim(in.Order)) {
			m.Order(in.Order)
		}

		if r, e := m.All(); e != nil {
			// 檢查是否為表不存在的錯誤
			if gstr.ContainsI(e.Error(), "doesn't exist") ||
				(gstr.ContainsI(e.Error(), "Table") && gstr.ContainsI(e.Error(), "doesn't exist")) {
				s.logger().Warningf(ctx, "Table %s does not exist, Error: %v", in.Table, e)
				return gerror.NewCode(consts.ErrTableNotExists, fmt.Sprintf("table %s does not exist", in.Table))
			}
			return gerror.NewCode(consts.ErrDatabaseError, e.Error())
		} else {
			fnMakeResult(r)
		}

		return nil
	}); err != nil {
		s.logger().Error(ctx, err)
	}

	return
}

func (s *sMariaDB) actionInsertData(ctx context.Context, msg *amqp.Delivery) (err error) {

	routeKey := gstr.TrimLeftStr(msg.RoutingKey, consts.RouteKeyMariadbPrefix)

	switch routeKey {
	default:
		return gerror.Newf("routeKey %s not found", routeKey)

	case consts.RouteKeyAMS:
		var amsData *ams.ResourceRecord
		_ = gconv.Struct(msg.Body, &amsData)
		if amsData == nil {

			return gerror.New("amsData is nil")
		}

		// 確保 ServiceID 有值，如果為空則使用默認值
		if g.IsEmpty(amsData.ServiceID) {
			amsData.ServiceID = consts.ServiceName // 使用服務名稱作為默認值
		}

		s.logger().Noticef(ctx, "insert ams data %v", gjson.New(amsData).MustToJsonIndentString())

		err = g.DB().Schema(amsData.Schema).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {

			if _, e := tx.Model(amsData.Table).Replace(amsData); e != nil {
				return e
			}
			return nil
		})

		return

	case consts.RouteKeyTenant, consts.RouteKeyQuizto, consts.RouteKeyChannelHub, consts.RouteKeyBrainHub:
		var mqMessage *model.MQMessage
		_ = gconv.Struct(msg.Body, &mqMessage)
		err = g.DB().Schema(mqMessage.Schema).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			if _, e := tx.Model(mqMessage.Table).Data(mqMessage.Data).Save(); e != nil {
				return e
			}
			return nil
		})
		return

	}

}
func (s *sMariaDB) actionUpdate(ctx context.Context, msg *amqp.Delivery) (err error) {
	routeKey := gstr.TrimLeftStr(msg.RoutingKey, consts.RouteKeyMariadbPrefix)
	switch routeKey {
	default:
		return gerror.Newf("routeKey %s not found", routeKey)

	case consts.RouteKeyAMS:
		return

	case consts.RouteKeyQuizto, consts.RouteKeyTenant, consts.RouteKeyChannelHub, consts.RouteKeyBrainHub:
		var mqMessage *model.MQMessage
		_ = gconv.Struct(msg.Body, &mqMessage)
		err = g.DB().Schema(mqMessage.Schema).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			if _, e := tx.Model(mqMessage.Table).
				Wheref(mqMessage.WhereConditions, mqMessage.WhereParams...).
				Data(mqMessage.Data).
				Update(); e != nil {
				return e
			}

			return nil
		})

	}
	return

}
func (s *sMariaDB) actionDelete(ctx context.Context, msg *amqp.Delivery) (err error) {
	routeKey := gstr.TrimLeftStr(msg.RoutingKey, consts.RouteKeyMariadbPrefix)
	switch routeKey {
	default:
		return gerror.Newf("routeKey %s not found", routeKey)

	case consts.RouteKeyAMS:
		var amsData *ams.ResourceDeleteData
		_ = gconv.Struct(msg.Body, &amsData)
		if amsData == nil {
			return gerror.New("amsData is nil")
		}

		// 安全地獲取 resource_type，處理可能的類型問題
		var resourceType string
		if headerValue, ok := msg.Headers["resource_type"]; ok {
			// 處理不同類型的 header 值
			switch v := headerValue.(type) {
			case string:
				resourceType = v
			case []byte:
				resourceType = string(v)
			default:
				// 嘗試使用 gconv 轉換
				resourceType = gconv.String(headerValue)
			}
		}

		if g.IsEmpty(resourceType) {
			return gerror.New("resource_type header is missing or empty")
		}

		s.logger().Debugf(ctx, "Processing delete for resource_type: %s", resourceType)
		return s.amsDelete(amsData, resourceType)

	case consts.RouteKeyTenant, consts.RouteKeyQuizto, consts.RouteKeyChannelHub, consts.RouteKeyBrainHub:
		var mqMessage *model.MQMessage
		_ = gconv.Struct(msg.Body, &mqMessage)
		if mqMessage == nil {
			return gerror.New("tenantData is nil")
		}
		err = g.DB().Schema(mqMessage.Schema).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			if _, e := tx.Model(mqMessage.Table).
				Wheref(mqMessage.WhereConditions, mqMessage.WhereParams...).
				Delete(); e != nil {
				return e
			}

			return nil
		})

	}
	return
}

func (s *sMariaDB) actionInsertOrUpdate(ctx context.Context, msg *amqp.Delivery) (err error) {
	routeKey := gstr.TrimLeftStr(msg.RoutingKey, consts.RouteKeyMariadbPrefix)
	switch routeKey {
	default:
		return gerror.Newf("routeKey %s not found", routeKey)

	case consts.RouteKeyAMS:
		var amsData *ams.ResourceRecord
		_ = gconv.Struct(msg.Body, &amsData)
		if amsData == nil {
			return gerror.New("amsData is nil")
		}

		// 確保 ServiceID 有值，如果為空則使用默認值
		if g.IsEmpty(amsData.ServiceID) {
			amsData.ServiceID = consts.ServiceName // 使用服務名稱作為默認值
		}

		// 安全地獲取 resource_type，處理可能的類型問題
		var resourceType string
		if headerValue, ok := msg.Headers["resource_type"]; ok {
			// 處理不同類型的 header 值
			switch v := headerValue.(type) {
			case string:
				resourceType = v
			case []byte:
				resourceType = string(v)
			default:
				// 嘗試使用 gconv 轉換
				resourceType = gconv.String(headerValue)
			}
		}

		if g.IsEmpty(resourceType) {
			return gerror.New("resource_type header is missing or empty")
		}

		field := consts.MapResourceTypeToField[resourceType]
		if g.IsEmpty(field) {
			return gerror.Newf("unsupported resource type: %s", resourceType)
		}

		err = g.DB().Schema(amsData.Schema).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {

			if exist, e := tx.Model(amsData.Table).Where(g.Map{"service_id": amsData.ServiceID}).Exist(); e != nil {
				return e
			} else if exist {
				// update - 使用智能合併邏輯
				hasChanges, e := s.mergeFieldContent(ctx, tx, amsData, field)
				if e != nil {
					return e
				}

				if hasChanges {
					s.logger().Infof(ctx, "Content merged and updated for service  %s, field %s", amsData.ServiceID, field)
				} else {
					s.logger().Debugf(ctx, "No changes detected for service %s, field %s, skipping update", amsData.ServiceID, field)
				}

			} else {
				// insert
				if _, e = tx.Model(amsData.Table).Data(amsData).Replace(); e != nil {
					return e
				}
			}

			return nil
		})
		return

	case consts.RouteKeyTenant, consts.RouteKeyQuizto, consts.RouteKeyChannelHub, consts.RouteKeyBrainHub:

		var mqMessage *model.MQMessage
		_ = gconv.Struct(msg.Body, &mqMessage)
		if mqMessage == nil {
			return gerror.New("tenantData is nil")
		}
		err = g.DB().Schema(mqMessage.Schema).Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {

			if exist, e := tx.Model(mqMessage.Table).Wheref(mqMessage.WhereConditions, mqMessage.WhereParams...).Exist(); e != nil {
				return e
			} else if exist {
				// update
				if _, e = tx.Model(mqMessage.Table).Wheref(mqMessage.WhereConditions, mqMessage.WhereParams...).
					Data(mqMessage.Data).Update(); e != nil {
					return e
				}

			} else {
				// save
				if _, e = tx.Model(mqMessage.Table).Data(mqMessage.Data).Save(); e != nil {
					return e
				}
			}

			return nil
		})

	}
	return
}

func (s *sMariaDB) amsDelete(amsData *ams.ResourceDeleteData, resourceType string) error {
	if amsData == nil || len(amsData.Contents) == 0 {
		return nil
	}

	field := consts.MapResourceTypeToField[resourceType]
	if field == "" {
		return gerror.Newf("unsupported resource type: %s", resourceType)
	}

	where := g.Map{}
	if !g.IsEmpty(amsData.ServiceID) {
		where["service_id"] = amsData.ServiceID
	} else {
		return gerror.New("service_id is empty")
	}

	m := g.DB().Schema(amsData.Schema).Model(amsData.Table).
		Where(where).Fields(field)

	result, err := m.All()
	if err != nil {
		return err
	}

	if len(result) == 0 {
		return nil
	}
	s.logger().Noticef(context.Background(), "result : %v", gjson.New(result).MustToJsonIndentString())
	s.logger().Noticef(context.Background(), "uplaod_files:%v,json:%v", result[0]["upload_files"], gjson.New(result[0]["upload_files"]).MustToJsonIndentString())
	filteredData, err := s.filterContentByType(result[0][field], amsData.Contents, resourceType)

	if err != nil {

		return err
	}
	s.logger().Noticef(context.Background(), "Contents: %v", gjson.New(amsData.Contents).MustToJsonIndentString())
	s.logger().Noticef(context.Background(), "field:%v ,filteredData: %v", field, gjson.New(filteredData).MustToJsonIndentString())

	//updateModel := g.DB().Schema(amsData.Schema).Model(amsData.Table).Where(where)
	//_, err = updateModel.Data(g.Map{field: filteredData}).Update()
	//if err != nil {
	//	s.logger().Error(context.Background(), err)
	//}
	return nil
}

func (s *sMariaDB) filterContentByType(data interface{}, contentsToRemove []string, resourceType string) (interface{}, error) {
	switch resourceType {
	case consts.ResourceTypeFile:
		var uploadFiles []*ams.FileContent
		if err := gconv.Structs(data, &uploadFiles); err != nil {
			return nil, err
		}
		return s.filterFiles(uploadFiles, contentsToRemove), nil

	case consts.ResourceTypeURL:
		var urlContents []*ams.WebSiteURLContent
		if err := gconv.Structs(data, &urlContents); err != nil {
			return nil, err
		}
		return s.filterURLs(urlContents, contentsToRemove), nil

	case consts.ResourceTypePlainText:
		var plainTextContents []*ams.PlainTextContent
		if err := gconv.Structs(data, &plainTextContents); err != nil {
			return nil, err
		}
		return s.filterPlainTexts(plainTextContents, contentsToRemove), nil

	case consts.ResourceTypeYoutubeLink:
		var youtubeContents []*ams.YoutubeContent
		if err := gconv.Structs(data, &youtubeContents); err != nil {
			return nil, err
		}
		return s.filterYoutubeLinks(youtubeContents, contentsToRemove), nil

	default:
		return nil, gerror.Newf("unsupported resource type: %s", resourceType)
	}
}

func (s *sMariaDB) filterFiles(files []*ams.FileContent, toRemove []string) []*ams.FileContent {
	removeSet := make(map[string]bool)
	for _, content := range toRemove {
		removeSet[content] = true
	}
	s.logger().Noticef(context.Background(), "removeSet: %v", gjson.New(removeSet).MustToJsonIndentString())
	s.logger().Noticef(context.Background(), "files: %v", gjson.New(files).MustToJsonIndentString())
	var filtered []*ams.FileContent
	for _, file := range files {
		if !removeSet[file.AccessPath] {
			filtered = append(filtered, file)
		}
	}
	return filtered
}

func (s *sMariaDB) filterURLs(urls []*ams.WebSiteURLContent, toRemove []string) []*ams.WebSiteURLContent {
	removeSet := make(map[string]bool)
	for _, content := range toRemove {
		removeSet[content] = true
	}

	var filtered []*ams.WebSiteURLContent
	for _, url := range urls {
		if !removeSet[url.WebSiteURL] {
			filtered = append(filtered, url)
		}
	}
	return filtered
}

func (s *sMariaDB) filterPlainTexts(texts []*ams.PlainTextContent, toRemove []string) []*ams.PlainTextContent {
	removeSet := make(map[string]bool)
	for _, content := range toRemove {
		s.logger().Noticef(context.Background(), "removed content: %v", content)
		removeSet[content] = true
	}

	var filtered []*ams.PlainTextContent
	for _, text := range texts {
		s.logger().Noticef(context.Background(), " original  text: %v", text.PlainText)
		if !removeSet[text.PlainText] {
			filtered = append(filtered, text)
		}
	}
	return filtered
}

func (s *sMariaDB) filterYoutubeLinks(links []*ams.YoutubeContent, toRemove []string) []*ams.YoutubeContent {
	removeSet := make(map[string]bool)
	for _, content := range toRemove {
		removeSet[content] = true
	}

	var filtered []*ams.YoutubeContent
	for _, link := range links {
		if !removeSet[link.YoutubeLink] {
			filtered = append(filtered, link)
		}
	}
	return filtered
}

func (s *sMariaDB) actionCreateSchema(ctx context.Context, msg *amqp.Delivery) (err error) {

	jsReq := gjson.New(msg.Body)
	if !jsReq.Contains("schema") {
		err = gerror.New("schema is not found")
		return
	}

	schema := jsReq.Get("schema").String()
	if g.IsEmpty(schema) {
		err = gerror.New("schema is empty")
		return
	}

	err = s.createSchemaIfNotExists(ctx, schema)

	return
}

func (s *sMariaDB) actionCreateTable(ctx context.Context, msg *amqp.Delivery) (err error) {
	jsReq := gjson.New(msg.Body)
	if !jsReq.Contains("schema") {
		err = gerror.New("schema is not found")
		return
	}

	tableScript := jsReq.Get("data").MapStrStr()
	if len(tableScript) == 0 {
		err = gerror.New("tableScript is empty")
		return
	}

	schemaName := jsReq.Get("schema").String()
	schemaDB := g.DB().Schema(schemaName)

	// 用於記錄需要查詢數據庫的表（緩存未命中的表）
	var tablesToCheck []string
	var cachedTables []string

	// 1. 先檢查緩存
	for table := range tableScript {
		if exists, fromCache := s.isTableExistsInCache(ctx, schemaName, table); fromCache && exists {
			s.logger().Debugf(ctx, "Table %s.%s exists in cache, skip creation", schemaName, table)
			cachedTables = append(cachedTables, table)
		} else {
			tablesToCheck = append(tablesToCheck, table)
		}
	}

	// 2. 如果有需要檢查的表，查詢數據庫
	var allTables *garray.StrArray
	if len(tablesToCheck) > 0 {
		tables, err := schemaDB.Tables(ctx)
		if err != nil {
			s.logger().Errorf(ctx, "Failed to get tables for schema %s: %v", schemaName, err)
			return err
		}
		allTables = garray.NewStrArrayFrom(tables)
	}

	// 3. 處理需要檢查的表
	for _, table := range tablesToCheck {
		script := tableScript[table]
		tableExists := allTables != nil && allTables.Contains(table)

		if !tableExists {
			// 執行創建表的 SQL
			if _, e := schemaDB.Exec(ctx, script); e != nil {
				s.logger().Errorf(ctx, "Failed to create table %s.%s: %v", schemaName, table, e)
				continue
			}
			s.logger().Debugf(ctx, "Table %s.%s created successfully", schemaName, table)
		}

		// 4. 更新緩存（無論表是否已存在，都更新緩存）
		if cacheErr := s.addTableToCache(ctx, schemaName, table); cacheErr != nil {
			s.logger().Warningf(ctx, "Failed to update table cache for %s.%s: %v", schemaName, table, cacheErr)
		}
	}

	s.logger().Infof(ctx, "Table creation completed for schema %s: %d cached, %d checked",
		schemaName, len(cachedTables), len(tablesToCheck))

	return
}

// sanitizeLogInput 脫敏處理日誌輸入，避免敏感信息洩露
func (s *sMariaDB) sanitizeLogInput(in model.ExecuteSQLInput) map[string]interface{} {
	return map[string]interface{}{
		"schema":     in.Schema,
		"sql_length": len(in.RawSQL),
		"sql_type":   s.extractSQLType(in.RawSQL),
	}
}

// sanitizeBatchLogInput 脫敏處理批量日誌輸入
func (s *sMariaDB) sanitizeBatchLogInput(in model.BatchExecuteSQLInput) map[string]interface{} {
	sqlTypes := make([]string, len(in.SQLList))
	totalLength := 0
	for i, sql := range in.SQLList {
		sqlTypes[i] = s.extractSQLType(sql)
		totalLength += len(sql)
	}

	return map[string]interface{}{
		"schema":           in.Schema,
		"sql_count":        len(in.SQLList),
		"total_sql_length": totalLength,
		"sql_types":        sqlTypes,
	}
}

// extractSQLType 提取 SQL 語句類型
func (s *sMariaDB) extractSQLType(sql string) string {
	sql = strings.TrimSpace(strings.ToUpper(sql))
	if sql == "" {
		return "UNKNOWN"
	}

	words := strings.Fields(sql)
	if len(words) == 0 {
		return "UNKNOWN"
	}

	// 處理複合操作
	if len(words) >= 2 {
		twoWords := fmt.Sprintf("%s %s", words[0], words[1])
		compoundOps := []string{
			"CREATE TABLE", "CREATE INDEX", "DROP TABLE", "DROP INDEX", "ALTER TABLE",
		}
		for _, op := range compoundOps {
			if strings.HasPrefix(sql, op) {
				return twoWords
			}
		}
	}

	return words[0]
}

// sanitizeGetContentsLogInput 脫敏處理查詢日誌輸入
func (s *sMariaDB) sanitizeGetContentsLogInput(in model.GetContentsReq) map[string]interface{} {
	result := map[string]interface{}{
		"schema": in.Schema,
		"table":  in.Table,
		"limit":  in.Limit,
		"order":  in.Order,
	}

	if !g.IsEmpty(in.RawSQL) {
		result["has_raw_sql"] = true
		result["sql_length"] = len(in.RawSQL)
		result["sql_type"] = s.extractSQLType(in.RawSQL)
	} else {
		result["has_raw_sql"] = false
	}

	if !g.IsEmpty(in.WhereCond) {
		result["has_where_condition"] = true
		result["where_length"] = len(in.WhereCond)
	} else {
		result["has_where_condition"] = false
	}

	if len(in.Fields) > 0 {
		result["field_count"] = len(in.Fields)
	}

	if len(in.Params) > 0 {
		result["param_count"] = len(in.Params)
	}

	return result
}

// getClientIP 獲取客戶端 IP 地址
func (s *sMariaDB) getClientIP(ctx context.Context) string {
	if request := ghttp.RequestFromCtx(ctx); request != nil {
		return request.GetClientIp()
	}
	return "unknown"
}

// mergeFieldContent 智能合併字段內容
func (s *sMariaDB) mergeFieldContent(ctx context.Context, tx gdb.TX, amsData *ams.ResourceRecord, field string) (bool, error) {
	// 查詢現有數據
	result, err := tx.Model(amsData.Table).
		Where(g.Map{"service_id": amsData.ServiceID}).
		Fields(field).
		One()

	if err != nil {
		s.logger().Errorf(ctx, "Failed to query existing data for field %s: %v", field, err)
		return false, err
	}

	if result.IsEmpty() {
		s.logger().Debugf(ctx, "No existing data found for service  %s, field %s", amsData.ServiceID, field)
		return false, nil
	}

	// 根據字段類型進行合併
	switch field {
	case consts.MapResourceTypeToField[consts.ResourceTypeFile]:
		return s.mergeAndUpdateUploadFiles(ctx, tx, amsData, result)
	case consts.MapResourceTypeToField[consts.ResourceTypeURL]:
		return s.mergeAndUpdateURLContents(ctx, tx, amsData, result)
	case consts.MapResourceTypeToField[consts.ResourceTypeYoutubeLink]:
		return s.mergeAndUpdateYoutubeContents(ctx, tx, amsData, result)
	case consts.MapResourceTypeToField[consts.ResourceTypePlainText]:
		return s.mergeAndUpdatePlainTextContents(ctx, tx, amsData, result)
	default:
		return false, gerror.Newf("unsupported field type: %s", field)
	}
}

// mergeAndUpdateUploadFiles 合併並更新 upload_files 字段
func (s *sMariaDB) mergeAndUpdateUploadFiles(ctx context.Context, tx gdb.TX, amsData *ams.ResourceRecord, existingResult gdb.Record) (bool, error) {
	// 解析現有數據
	var existingFiles []*ams.FileContent
	if !existingResult[consts.MapResourceTypeToField[consts.ResourceTypeFile]].IsEmpty() {
		if err := gconv.Structs(existingResult[consts.MapResourceTypeToField[consts.ResourceTypeFile]], &existingFiles); err != nil {
			s.logger().Errorf(ctx, "Failed to parse existing upload_files: %v", err)
			return false, err
		}
	}

	// 合併數據
	mergedFiles := s.mergeUploadFiles(existingFiles, amsData.UploadFiles)

	// 檢查是否有變化
	if len(mergedFiles) == len(existingFiles) {
		s.logger().Debugf(ctx, "No new files to add for service %s", amsData.ServiceID)
		return false, nil
	}

	// 執行更新
	updateData := g.Map{consts.MapResourceTypeToField[consts.ResourceTypeFile]: mergedFiles}
	if _, err := tx.Model(amsData.Table).
		Where(g.Map{"service_id": amsData.ServiceID}).
		Data(updateData).
		Update(); err != nil {
		s.logger().Errorf(ctx, "Failed to update upload_files: %v", err)
		return false, err
	}

	s.logger().Infof(ctx, "Successfully merged upload_files: %d existing + %d new = %d total",
		len(existingFiles), len(amsData.UploadFiles), len(mergedFiles))
	return true, nil
}

// mergeAndUpdateURLContents 合併並更新 url_contents 字段
func (s *sMariaDB) mergeAndUpdateURLContents(ctx context.Context, tx gdb.TX, amsData *ams.ResourceRecord, existingResult gdb.Record) (bool, error) {
	// 解析現有數據
	var existingURLs []*ams.WebSiteURLContent
	if !existingResult[consts.MapResourceTypeToField[consts.ResourceTypeURL]].IsEmpty() {
		if err := gconv.Structs(existingResult[consts.MapResourceTypeToField[consts.ResourceTypeURL]], &existingURLs); err != nil {
			s.logger().Errorf(ctx, "Failed to parse existing url_contents: %v", err)
			return false, err
		}
	}

	// 合併數據
	mergedURLs := s.mergeURLContents(existingURLs, amsData.URLContents)

	// 檢查是否有變化
	if len(mergedURLs) == len(existingURLs) {
		s.logger().Debugf(ctx, "No new URLs to add for  service  %s", amsData.ServiceID)
		return false, nil
	}

	// 執行更新
	updateData := g.Map{consts.MapResourceTypeToField[consts.ResourceTypeURL]: mergedURLs}
	if _, err := tx.Model(amsData.Table).
		Where(g.Map{"service_id": amsData.ServiceID}).
		Data(updateData).
		Update(); err != nil {
		s.logger().Errorf(ctx, "Failed to update url_contents: %v", err)
		return false, err
	}

	s.logger().Infof(ctx, "Successfully merged url_contents: %d existing + %d new = %d total",
		len(existingURLs), len(amsData.URLContents), len(mergedURLs))
	return true, nil
}

// mergeAndUpdateYoutubeContents 合併並更新 youtube_contents 字段
func (s *sMariaDB) mergeAndUpdateYoutubeContents(ctx context.Context, tx gdb.TX, amsData *ams.ResourceRecord, existingResult gdb.Record) (bool, error) {
	// 解析現有數據
	var existingYoutube []*ams.YoutubeContent
	if !existingResult[consts.MapResourceTypeToField[consts.ResourceTypeYoutubeLink]].IsEmpty() {
		if err := gconv.Structs(existingResult[consts.MapResourceTypeToField[consts.ResourceTypeYoutubeLink]], &existingYoutube); err != nil {
			s.logger().Errorf(ctx, "Failed to parse existing youtube_contents: %v", err)
			return false, err
		}
	}

	// 合併數據
	mergedYoutube := s.mergeYoutubeContents(existingYoutube, amsData.YoutubeContents)

	// 檢查是否有變化
	if len(mergedYoutube) == len(existingYoutube) {
		s.logger().Debugf(ctx, "No new YouTube links to add for service  %s", amsData.ServiceID)
		return false, nil
	}

	// 執行更新
	updateData := g.Map{consts.MapResourceTypeToField[consts.ResourceTypeYoutubeLink]: mergedYoutube}
	if _, err := tx.Model(amsData.Table).
		Where(g.Map{"service_id": amsData.ServiceID}).
		Data(updateData).
		Update(); err != nil {
		s.logger().Errorf(ctx, "Failed to update youtube_contents: %v", err)
		return false, err
	}

	s.logger().Infof(ctx, "Successfully merged youtube_contents: %d existing + %d new = %d total",
		len(existingYoutube), len(amsData.YoutubeContents), len(mergedYoutube))
	return true, nil
}

// mergeAndUpdatePlainTextContents 合併並更新 plain_text_contents 字段
func (s *sMariaDB) mergeAndUpdatePlainTextContents(ctx context.Context, tx gdb.TX, amsData *ams.ResourceRecord, existingResult gdb.Record) (bool, error) {
	// 解析現有數據
	var existingTexts []*ams.PlainTextContent
	if !existingResult[consts.MapResourceTypeToField[consts.ResourceTypePlainText]].IsEmpty() {
		if err := gconv.Structs(existingResult[consts.MapResourceTypeToField[consts.ResourceTypePlainText]], &existingTexts); err != nil {
			s.logger().Errorf(ctx, "Failed to parse existing plain_text_contents: %v", err)
			return false, err
		}
	}

	// 合併數據
	mergedTexts := s.mergePlainTextContents(existingTexts, amsData.PlainTextContents)

	// 檢查是否有變化
	if len(mergedTexts) == len(existingTexts) {
		s.logger().Debugf(ctx, "No new plain texts to add for service  %s", amsData.ServiceID)
		return false, nil
	}

	// 執行更新
	updateData := g.Map{consts.MapResourceTypeToField[consts.ResourceTypePlainText]: mergedTexts}
	if _, err := tx.Model(amsData.Table).
		Where(g.Map{"service_id": amsData.ServiceID}).
		Data(updateData).
		Update(); err != nil {
		s.logger().Errorf(ctx, "Failed to update plain_text_contents: %v", err)
		return false, err
	}

	s.logger().Infof(ctx, "Successfully merged plain_text_contents: %d existing + %d new = %d total",
		len(existingTexts), len(amsData.PlainTextContents), len(mergedTexts))
	return true, nil
}

// mergeUploadFiles 合併 upload_files 數組，基於 file_name 去重
func (s *sMariaDB) mergeUploadFiles(existing, new []*ams.FileContent) []*ams.FileContent {
	if len(existing) == 0 {
		return new
	}
	if len(new) == 0 {
		return existing
	}

	// 使用 gset 存儲已存在的 file_name
	existingNames := gset.NewStrSet(true)
	for _, item := range existing {
		if item != nil && !g.IsEmpty(item.FileName) {
			existingNames.Add(item.FileName)
		}
	}

	// 合併結果
	result := make([]*ams.FileContent, 0, len(existing)+len(new))
	result = append(result, existing...)

	// 添加新的不重複項目
	for _, item := range new {
		if item != nil && !g.IsEmpty(item.FileName) && !existingNames.Contains(item.FileName) {
			result = append(result, item)
		}
	}

	return result
}

// mergeURLContents 合併 url_contents 數組，基於 website_url 去重
func (s *sMariaDB) mergeURLContents(existing, new []*ams.WebSiteURLContent) []*ams.WebSiteURLContent {
	if len(existing) == 0 {
		return new
	}
	if len(new) == 0 {
		return existing
	}

	// 使用 gset 存儲已存在的 website_url
	existingURLs := gset.NewStrSet(true)
	for _, item := range existing {
		if item != nil && !g.IsEmpty(item.WebSiteURL) {
			existingURLs.Add(item.WebSiteURL)
		}
	}

	// 合併結果
	result := make([]*ams.WebSiteURLContent, 0, len(existing)+len(new))
	result = append(result, existing...)

	// 添加新的不重複項目
	for _, item := range new {
		if item != nil && !g.IsEmpty(item.WebSiteURL) && !existingURLs.Contains(item.WebSiteURL) {
			result = append(result, item)
		}
	}

	return result
}

// mergeYoutubeContents 合併 youtube_contents 數組，基於 youtube_link 去重
func (s *sMariaDB) mergeYoutubeContents(existing, new []*ams.YoutubeContent) []*ams.YoutubeContent {
	if len(existing) == 0 {
		return new
	}
	if len(new) == 0 {
		return existing
	}

	// 使用 gset 存儲已存在的 youtube_link
	existingLinks := gset.NewStrSet(true)
	for _, item := range existing {
		if item != nil && !g.IsEmpty(item.YoutubeLink) {
			existingLinks.Add(item.YoutubeLink)
		}
	}

	// 合併結果
	result := make([]*ams.YoutubeContent, 0, len(existing)+len(new))
	result = append(result, existing...)

	// 添加新的不重複項目
	for _, item := range new {
		if item != nil && !g.IsEmpty(item.YoutubeLink) && !existingLinks.Contains(item.YoutubeLink) {
			result = append(result, item)
		}
	}

	return result
}

// mergePlainTextContents 合併 plain_text_contents 數組，基於 plain_text 去重
func (s *sMariaDB) mergePlainTextContents(existing, new []*ams.PlainTextContent) []*ams.PlainTextContent {
	if len(existing) == 0 {
		return new
	}
	if len(new) == 0 {
		return existing
	}

	// 使用 gset 存儲已存在的 plain_text
	existingTexts := gset.NewStrSet(true)
	for _, item := range existing {
		if item != nil && !g.IsEmpty(item.PlainText) {
			existingTexts.Add(item.PlainText)
		}
	}

	// 合併結果
	result := make([]*ams.PlainTextContent, 0, len(existing)+len(new))
	result = append(result, existing...)

	// 添加新的不重複項目
	for _, item := range new {
		if item != nil && !g.IsEmpty(item.PlainText) && !existingTexts.Contains(item.PlainText) {
			result = append(result, item)
		}
	}

	return result
}
