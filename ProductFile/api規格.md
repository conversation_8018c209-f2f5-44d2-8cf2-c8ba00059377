# DataSyncHub API 規格文檔

## 📋 專案概述

### 專案簡介
DataSyncHub 是一個基於 GoFrame 框架構建的企業級數據同步中心，專為現代 AI 應用和多數據源環境設計。它提供了一個統一的平台來管理和同步不同類型的數據存儲系統，包括關係型數據庫（MariaDB）、向量數據庫（Weaviate）和消息隊列系統（RabbitMQ）。

### 技術架構
- **框架**: GoFrame v2.9.0
- **語言**: Go 1.24
- **配置管理**: Nacos 配置中心
- **數據庫**: MariaDB/MySQL
- **向量數據庫**: Weaviate
- **消息隊列**: RabbitMQ
- **服務發現**: Nacos 服務註冊與發現

### 主要特性
1. **多數據源整合**: 統一管理關係型數據庫和向量數據庫
2. **實時數據同步**: 基於消息隊列的異步數據處理
3. **AI 應用支持**: 向量嵌入存儲和相似性搜索
4. **微服務架構**: 支持服務發現和負載均衡
5. **配置熱更新**: 基於 Nacos 的動態配置管理
6. **安全機制**: 服務白名單和內部服務認證

### 使用場景
- AI 應用數據管理和向量檢索
- 多數據源整合和實時同步
- 微服務間數據協調
- 企業級數據中心建設

## 🏗️ 系統架構

### 架構層次
```
┌─────────────────────────────────────────────────────────────┐
│                        API Gateway                          │
├─────────────────────────────────────────────────────────────┤
│                     HTTP API Layer                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ Execute API │  │ Query API   │  │ Vector API  │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│                   Controller Layer                          │
├─────────────────────────────────────────────────────────────┤
│                   Business Logic Layer                      │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ MariaDB     │  │ Vector      │  │ MessageQ    │         │
│  │ Logic       │  │ Logic       │  │ Logic       │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│                     Service Layer                           │
├─────────────────────────────────────────────────────────────┤
│                      Data Layer                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   MariaDB   │  │  Weaviate   │  │  RabbitMQ   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

### 核心組件
1. **API 層**: 提供 RESTful HTTP 接口
2. **控制器層**: 處理 HTTP 請求和響應
3. **業務邏輯層**: 實現核心業務邏輯
4. **服務層**: 定義服務接口和實現
5. **數據層**: 與外部數據源交互

## 🔐 認證和安全

### 服務白名單機制
系統採用基於服務白名單的認證機制：

#### 配置方式
```yaml
system:
  white_list: "service1,service2,service3"
```

#### 認證流程
1. 客戶端在請求頭中設置 `X-SERVICE` 字段
2. 系統檢查該服務是否在白名單中
3. 白名單驗證通過後允許訪問 API
4. 驗證失敗返回 403 Forbidden

#### 請求頭格式
```http
X-SERVICE: your-service-name
Content-Type: application/json
```

### 內部服務豁免
- 內部服務可以不受安全檢查影響
- 通過配置管理內部服務列表
- 支持動態更新服務白名單

## 📊 通用響應格式

### 成功響應
```json
{
  "code": 0,
  "message": "success",
  "cost": "15ms",
  "data": {
    // 具體數據內容
  }
}
```

### 錯誤響應
```json
{
  "code": -1,
  "message": "error description",
  "cost": "10ms"
}
```

### 狀態碼定義
| 狀態碼 | 含義 | 說明 |
|--------|------|------|
| 0 | 成功 | 請求處理成功 |
| -1 | 失敗 | 通用錯誤 |
| -2 | 無效服務 | 服務未在白名單中 |
| 1001 | 表不存在 | 查詢的數據表不存在 |
| 1002 | 數據庫錯誤 | 數據庫操作失敗 |
| 1003 | 無效輸入 | 請求參數無效 |
| 1004 | 未授權 | 缺少必要的授權信息 |

## 🔧 Execute API

Execute API 提供 SQL 執行功能，支持單條和批量 SQL 語句執行。

### 基礎路徑
```
/executeapi/v1
```

### 1. 執行單條 SQL

#### 端點
```http
POST /executeapi/v1/executeSQL
```

#### 請求格式
```json
{
  "schema": "dsh",
  "raw_sql": "INSERT INTO users (name, email) VALUES ('張三', '<EMAIL>')"
}
```

#### 請求參數
| 參數 | 類型 | 必填 | 說明 |
|------|------|------|------|
| schema | string | 是 | 數據庫 schema 名稱 |
| raw_sql | string | 是 | 要執行的 SQL 語句 |

#### 響應格式
```json
{
  "code": 0,
  "message": "success",
  "cost": "15ms"
}
```

#### 響應參數
| 參數 | 類型 | 說明 |
|------|------|------|
| code | int | 狀態碼 |
| message | string | 響應消息 |
| cost | string | 執行耗時 |

### 2. 批量執行 SQL

#### 端點
```http
POST /executeapi/v1/batchExecuteSQL
```

#### 請求格式
```json
{
  "schema": "dsh",
  "sql_list": [
    "INSERT INTO users (name, email) VALUES ('張三', '<EMAIL>')",
    "INSERT INTO users (name, email) VALUES ('李四', '<EMAIL>')",
    "UPDATE users SET email = '<EMAIL>' WHERE name = '張三'"
  ]
}
```

#### 請求參數
| 參數 | 類型 | 必填 | 說明 |
|------|------|------|------|
| schema | string | 是 | 數據庫 schema 名稱 |
| sql_list | array | 是 | SQL 語句列表 |

#### 響應格式
```json
{
  "code": 0,
  "message": "success",
  "cost": "45ms",
  "total_count": 3,
  "success_count": 2,
  "fail_count": 1,
  "errors": [
    "第3條SQL執行失敗: Duplicate entry '<EMAIL>' for key 'email'"
  ]
}
```

#### 響應參數
| 參數 | 類型 | 說明 |
|------|------|------|
| code | int | 狀態碼 |
| message | string | 響應消息 |
| cost | string | 執行耗時 |
| total_count | int | 總SQL條數 |
| success_count | int | 成功執行條數 |
| fail_count | int | 失敗執行條數 |
| errors | array | 錯誤信息列表 |

## 🔍 Query API

Query API 提供數據查詢功能，支持靈活的條件查詢和結果過濾。

### 基礎路徑
```
/queryapi/v1
```

### 1. 數據查詢

#### 端點
```http
POST /queryapi/v1/getContents
```

#### 請求格式
```json
{
  "schema": "dsh",
  "table": "users",
  "where_cond": "name = ? AND status = ?",
  "params": ["張三", "active"],
  "fields": ["id", "name", "email", "created_at"],
  "limit": 10,
  "order": "created_at DESC"
}
```

#### 請求參數
| 參數 | 類型 | 必填 | 說明 |
|------|------|------|------|
| schema | string | 否 | 數據庫 schema 名稱 |
| table | string | 否 | 表名稱 |
| where_cond | string | 否 | WHERE 條件 |
| params | array | 否 | 參數值列表 |
| fields | array | 否 | 查詢字段列表 |
| limit | int | 否 | 限制返回記錄數 |
| raw_sql | string | 否 | 原始 SQL 查詢 |
| order | string | 否 | 排序條件 |

#### 響應格式
```json
{
  "contents": [
    {
      "id": 1,
      "name": "張三",
      "email": "<EMAIL>",
      "created_at": "2024-01-15T10:30:00Z"
    },
    {
      "id": 2,
      "name": "李四",
      "email": "<EMAIL>",
      "created_at": "2024-01-16T14:20:00Z"
    }
  ],
  "code": 0,
  "message": "success",
  "cost": "12ms"
}
```

#### 響應參數
| 參數 | 類型 | 說明 |
|------|------|------|
| contents | array | 查詢結果數據 |
| code | int | 狀態碼 |
| message | string | 響應消息 |
| cost | string | 執行耗時 |

#### 使用原始 SQL 查詢
```json
{
  "raw_sql": "SELECT u.*, p.profile_name FROM users u LEFT JOIN profiles p ON u.id = p.user_id WHERE u.status = 'active' ORDER BY u.created_at DESC LIMIT 20"
}
```

## 🧠 Vector API

Vector API 提供向量數據庫操作功能，支持集合管理、數據創建、相似性搜索和混合搜索等功能。

### 基礎路徑
```
/vector/v1
```

### 1. 創建集合

#### 端點
```http
POST /vector/v1/createCollection
```

#### 請求格式
```json
{
  "collection": "documents",
  "properties": [
    {
      "name": "title",
      "dataType": "text"
    },
    {
      "name": "content",
      "dataType": "text"
    },
    {
      "name": "category",
      "dataType": "string"
    }
  ],
  "vectorizer": "text2vec-openai",
  "tenants": ["tenant1", "tenant2"],
  "renew_settings": false
}
```

#### 請求參數
| 參數 | 類型 | 必填 | 說明 |
|------|------|------|------|
| collection | string | 是 | 集合名稱 |
| properties | array | 是 | 屬性定義列表 |
| vectorizer | string | 否 | 向量化器類型 |
| tenants | array | 否 | 租戶列表 |
| renew_settings | bool | 否 | 是否更新設置 |

#### 響應格式
```json
{
  "code": 0,
  "message": "success",
  "cost": "120ms"
}
```

### 2. 創建單條數據

#### 端點
```http
POST /vector/v1/createData
```

#### 請求格式
```json
{
  "tenant": "tenant1",
  "collection": "documents",
  "properties": {
    "title": "AI 技術發展趨勢",
    "content": "人工智能技術在近年來發展迅速，特別是在自然語言處理和計算機視覺領域...",
    "category": "technology"
  }
}
```

#### 請求參數
| 參數 | 類型 | 必填 | 說明 |
|------|------|------|------|
| tenant | string | 否 | 租戶標識 |
| collection | string | 是 | 集合名稱 |
| properties | object | 是 | 數據屬性 |

#### 響應格式
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "code": 0,
  "message": "success",
  "cost": "85ms"
}
```

### 3. 批量創建數據

#### 端點
```http
POST /vector/v1/batchCreateData
```

#### 請求格式
```json
{
  "data": [
    {
      "tenant": "tenant1",
      "collection": "documents",
      "properties": {
        "title": "機器學習基礎",
        "content": "機器學習是人工智能的一個重要分支...",
        "category": "education"
      }
    },
    {
      "tenant": "tenant1",
      "collection": "documents",
      "properties": {
        "title": "深度學習應用",
        "content": "深度學習在圖像識別、語音識別等領域有廣泛應用...",
        "category": "technology"
      }
    }
  ]
}
```

#### 響應格式
```json
{
  "ids": [
    "550e8400-e29b-41d4-a716-446655440001",
    "550e8400-e29b-41d4-a716-446655440002"
  ],
  "total": 2,
  "success": 2,
  "fail": 0,
  "code": 0,
  "message": "success",
  "cost": "150ms"
}
```

### 4. 相似性搜索

#### 端點
```http
POST /vector/v1/similaritySearch
```

#### 請求格式
```json
{
  "tenant": "tenant1",
  "collection": "documents",
  "properties": ["title", "content", "category"],
  "vector": [0.1, 0.2, 0.3, ...],
  "distance": 0.7,
  "limit": 5,
  "filter": "{\"path\":[\"category\"],\"operator\":\"Equal\",\"valueString\":\"technology\"}",
  "original_additional": false
}
```

#### 請求參數
| 參數 | 類型 | 必填 | 說明 |
|------|------|------|------|
| tenant | string | 否 | 租戶標識 |
| collection | string | 是 | 集合名稱 |
| properties | array | 是 | 返回屬性列表 |
| vector | array | 是 | 查詢向量 |
| distance | float | 是 | 距離閾值 |
| limit | int | 是 | 返回結果數量 |
| filter | string | 否 | 過濾條件 |
| original_additional | bool | 否 | 是否保留原始附加信息 |

#### 響應格式
```json
{
  "results": [
    {
      "title": "AI 技術發展趨勢",
      "content": "人工智能技術在近年來發展迅速...",
      "category": "technology",
      "distance": 0.15,
      "certainty": 0.85
    }
  ],
  "code": 0,
  "message": "success",
  "cost": "45ms"
}
```

### 5. 混合搜索

#### 端點
```http
POST /vector/v1/hybridSearch
```

#### 請求格式
```json
{
  "tenant": "tenant1",
  "collection": "documents",
  "query": "人工智能技術發展",
  "properties": ["title", "content"],
  "vector": [0.1, 0.2, 0.3, ...],
  "filter": "{\"path\":[\"category\"],\"operator\":\"Equal\",\"valueString\":\"technology\"}",
  "alpha": 0.5,
  "property_weight": ["title^2", "content^1"],
  "fusion_type": "rankedFusion",
  "limit": 10,
  "original_additional": false
}
```

#### 請求參數
| 參數 | 類型 | 必填 | 說明 |
|------|------|------|------|
| tenant | string | 否 | 租戶標識 |
| collection | string | 是 | 集合名稱 |
| query | string | 是 | 文本查詢 |
| properties | array | 是 | 返回屬性列表 |
| vector | array | 是 | 查詢向量 |
| filter | string | 是 | 過濾條件 |
| alpha | float | 是 | 混合權重 (0-1) |
| property_weight | array | 是 | 屬性權重 |
| fusion_type | string | 是 | 融合類型 |
| limit | int | 是 | 返回結果數量 |

### 6. 獲取集合屬性

#### 端點
```http
POST /vector/v1/getProperties
```

#### 請求格式
```json
{
  "collection": "documents"
}
```

#### 響應格式
```json
{
  "properties": [
    {
      "name": "title",
      "dataType": "text"
    },
    {
      "name": "content",
      "dataType": "text"
    }
  ],
  "code": 0,
  "message": "success",
  "cost": "8ms"
}
```

### 7. 獲取屬性名稱

#### 端點
```http
POST /vector/v1/getPropertyNames
```

#### 請求格式
```json
{
  "collection": "documents"
}
```

#### 響應格式
```json
{
  "names": ["title", "content", "category"],
  "code": 0,
  "message": "success",
  "cost": "5ms"
}
```

### 8. 獲取所有記錄

#### 端點
```http
POST /vector/v1/getAllRecords
```

#### 請求格式
```json
{
  "tenant": "tenant1",
  "collection": "documents",
  "properties": ["title", "content"],
  "limit": 100,
  "original_additional": false
}
```

### 9. 獲取租戶和集合信息

#### 端點
```http
POST /vector/v1/getTenantAndCollections
```

#### 請求格式
```json
{
  "collections": ["documents", "images", "videos"]
}
```

#### 響應格式
```json
{
  "tenant_collections": {
    "documents": ["tenant1", "tenant2"],
    "images": ["tenant1"],
    "videos": ["tenant2"]
  },
  "code": 0,
  "message": "success",
  "cost": "12ms"
}
```

## 📨 消息隊列規格

DataSyncHub 使用 RabbitMQ 實現異步數據同步，支持多種路由鍵和消息類型。

### 消息隊列架構

#### Exchange 配置
- **Exchange 名稱**: `dsh`
- **Exchange 類型**: `topic`
- **持久化**: `true`

#### Queue 配置
- **Queue 類型**: 自動生成臨時隊列
- **持久化**: `true`
- **自動刪除**: `true`
- **獨占**: `true`

### 路由鍵結構

路由鍵採用分層結構：`{service}.{resource}`

#### 服務前綴
| 前綴 | 說明 |
|------|------|
| `mariadb.` | MariaDB 相關操作 |
| `weaviate.` | Weaviate 向量數據庫操作 |

#### 資源類型
| 資源 | 說明 |
|------|------|
| `ams` | AMS 系統資源 |
| `tenant` | 租戶資源 |
| `quizto` | Quizto 系統資源 |
| `channelHub` | ChannelHub 資源 |
| `brainHub` | BrainHub 資源 |

#### 完整路由鍵列表
```
mariadb.ams
mariadb.tenant
mariadb.quizto
mariadb.channelHub
mariadb.brainHub
weaviate.ams
weaviate.tenant
weaviate.quizto
```

### MariaDB 消息類型

#### 支持的動作類型
| 動作 | 說明 |
|------|------|
| `insert` | 插入數據 |
| `delete` | 刪除數據 |
| `update` | 更新數據 |
| `updateOrInsert` | 更新或插入數據 |
| `createSchema` | 創建數據庫 Schema |
| `createTable` | 創建數據表 |

#### 消息格式示例
```json
{
  "action": "insert",
  "schema": "dsh",
  "table": "resource_records",
  "data": {
    "upload_user_id": "user123",
    "upload_files": [...],
    "url_contents": [...],
    "youtube_contents": [...],
    "plain_text_contents": [...]
  }
}
```

### Weaviate 消息類型

#### 支持的動作類型
| 動作 | 說明 |
|------|------|
| `create_collection` | 創建集合 |
| `add_new_properties` | 添加新屬性 |
| `create_data` | 創建數據 |
| `update_properties` | 更新屬性 |
| `create_tenant` | 創建租戶 |
| `clear_data_by_filter` | 按條件清除數據 |
| `empty_collection` | 清空集合 |
| `delete_collection` | 刪除集合 |
| `delete_tenants` | 刪除租戶 |
| `update_vector` | 更新向量 |

#### 消息格式示例
```json
{
  "action": "create_data",
  "tenant": "tenant1",
  "collection": "documents",
  "properties": {
    "title": "技術文檔",
    "content": "這是一份關於 AI 技術的詳細文檔...",
    "category": "technology"
  }
}
```

### 消息處理流程

1. **消息發送**: 外部系統發送消息到 RabbitMQ Exchange
2. **路由分發**: 根據路由鍵將消息路由到對應的隊列
3. **消息接收**: DataSyncHub 接收並解析消息
4. **處理分發**: 根據路由鍵前綴分發到對應的處理器
5. **業務處理**: 執行具體的數據庫或向量數據庫操作
6. **結果記錄**: 記錄處理結果和錯誤信息

## ⚙️ 配置參數說明

DataSyncHub 採用 Nacos 配置中心進行集中配置管理。

### 服務器配置
```yaml
server:
  address: ":8087"           # 服務監聽地址
  openapiPath: "/api.json"   # OpenAPI 文檔路徑
  swaggerPath: "/swagger"    # Swagger UI 路徑
```

### 日誌配置
```yaml
logger:
  level: "all"                    # 日誌級別
  stdout: true                    # 是否輸出到控制台
  path: "./logs"                  # 日誌文件路徑
  file: "dsh_{Y-m-d}.log"        # 日誌文件名格式
  rotateExpire: "1d"             # 日誌輪轉週期
  rotateBackupLimit: 1           # 備份文件數量
  rotateBackupExpire: "7d"       # 備份文件保留時間
  rotateBackupCompress: 9        # 壓縮級別
```

### 數據庫配置
```yaml
database:
  default:
    link: "mysql:root:password@tcp(127.0.0.1:3306)?charset=utf8mb4&loc=Local&parseTime=true"
    debug: true
  dsh:
    link: "mysql:root:password@tcp(127.0.0.1:3306)/dsh?charset=utf8mb4&loc=Local&parseTime=true"
    debug: true
  logger:
    path: "logs/db"              # 數據庫日誌路徑
    level: "all"                 # 數據庫日誌級別
    stdout: false                # 不輸出到控制台
    rotateExpire: "1d"
    rotateBackupLimit: 1
    rotateBackupExpire: "7d"
    rotateBackupCompress: 9
```

### RabbitMQ 配置
```yaml
rabbitMQ:
  url: "amqp://admin:admin@127.0.0.1:5672/"  # RabbitMQ 連接 URL
```

### 向量嵌入配置
```yaml
vectorEmbeddings:
  embedding:
    provider: azure              # 提供者: azure, google.studio, google.vertex
    azure:
      resourceName: "your-resource"
      deploymentId: "text-embedding-ada-002"
      api_key: "your-api-key"
    google:
      projectId: "your-project-id"
      modelId: "your-model-id"
      studioAPIKey: "your-studio-key"
```

### Weaviate 配置
```yaml
weaviate:
  host: "localhost:8080"         # Weaviate 服務地址
  scheme: "http"                 # 連接協議
```

### 系統配置
```yaml
system:
  white_list: "service1,service2,service3"  # 服務白名單
```

### Redis 緩存配置
```yaml
redis:
  default:
    address: "127.0.0.1:6379"    # Redis 服務地址
    db: 0                        # 數據庫編號
    pass: ""                     # 密碼
```

## 🔧 開發和部署

### 本地開發環境

#### 前置條件
- Go 1.24+
- MariaDB/MySQL 8.0+
- Weaviate 1.20+
- RabbitMQ 3.12+
- Nacos 2.2.0+
- Redis 6.0+

#### 啟動步驟
1. **啟動依賴服務**
   ```bash
   # 啟動 Nacos
   ./nacos/bin/startup.sh -m standalone

   # 啟動 MariaDB
   systemctl start mariadb

   # 啟動 RabbitMQ
   systemctl start rabbitmq-server

   # 啟動 Weaviate
   docker run -d -p 8080:8080 weaviate/weaviate:latest
   ```

2. **配置 Nacos**
   - 訪問 http://localhost:8848/nacos
   - 創建配置文件 `dsh.yaml`
   - 設置相應的配置參數

3. **啟動應用**
   ```bash
   go run main.go
   ```

### 生產環境部署

#### Docker 部署
```dockerfile
FROM golang:1.24-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -o dataSyncHub main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/dataSyncHub .
CMD ["./dataSyncHub"]
```

#### Kubernetes 部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: datasynchub
spec:
  replicas: 3
  selector:
    matchLabels:
      app: datasynchub
  template:
    metadata:
      labels:
        app: datasynchub
    spec:
      containers:
      - name: datasynchub
        image: datasynchub:latest
        ports:
        - containerPort: 8087
        env:
        - name: NACOS_SERVER
          value: "nacos-service:8848"
```

## 📝 使用示例

### 完整工作流程示例

#### 1. 創建向量集合
```bash
curl -X POST http://localhost:8087/vector/v1/createCollection \
  -H "Content-Type: application/json" \
  -H "X-SERVICE: my-service" \
  -d '{
    "collection": "documents",
    "properties": [
      {"name": "title", "dataType": "text"},
      {"name": "content", "dataType": "text"}
    ],
    "vectorizer": "text2vec-openai"
  }'
```

#### 2. 插入數據到 MariaDB
```bash
curl -X POST http://localhost:8087/executeapi/v1/executeSQL \
  -H "Content-Type: application/json" \
  -H "X-SERVICE: my-service" \
  -d '{
    "schema": "dsh",
    "raw_sql": "INSERT INTO documents (title, content) VALUES (\"AI 技術\", \"人工智能發展趨勢\")"
  }'
```

#### 3. 創建向量數據
```bash
curl -X POST http://localhost:8087/vector/v1/createData \
  -H "Content-Type: application/json" \
  -H "X-SERVICE: my-service" \
  -d '{
    "collection": "documents",
    "properties": {
      "title": "AI 技術",
      "content": "人工智能發展趨勢分析..."
    }
  }'
```

#### 4. 執行相似性搜索
```bash
curl -X POST http://localhost:8087/vector/v1/similaritySearch \
  -H "Content-Type: application/json" \
  -H "X-SERVICE: my-service" \
  -d '{
    "collection": "documents",
    "properties": ["title", "content"],
    "vector": [0.1, 0.2, 0.3, ...],
    "distance": 0.7,
    "limit": 5
  }'
```

#### 5. 查詢 MariaDB 數據
```bash
curl -X POST http://localhost:8087/queryapi/v1/getContents \
  -H "Content-Type: application/json" \
  -H "X-SERVICE: my-service" \
  -d '{
    "schema": "dsh",
    "table": "documents",
    "where_cond": "title LIKE ?",
    "params": ["%AI%"],
    "limit": 10
  }'
```

## 🚨 錯誤處理和故障排除

### 常見錯誤碼
| 錯誤碼 | 錯誤信息 | 解決方案 |
|--------|----------|----------|
| -1 | failed | 檢查請求參數和服務狀態 |
| -2 | invalid service | 確認服務在白名單中 |
| 1001 | table does not exist | 確認表名正確或創建相應的表 |
| 1002 | database error | 檢查數據庫連接和權限 |
| 1003 | invalid input | 驗證請求參數格式和內容 |
| 1004 | unauthorized | 檢查認證信息和權限設置 |
| 403 | Forbidden | 檢查 X-SERVICE 請求頭 |
| 500 | Internal Server Error | 查看服務日誌 |

### 故障排除步驟
1. **檢查服務狀態**: 確認所有依賴服務正常運行
2. **查看日誌**: 檢查應用和數據庫日誌
3. **驗證配置**: 確認 Nacos 配置正確
4. **網絡連通性**: 測試各服務間的網絡連接
5. **資源使用**: 監控 CPU、內存和磁盤使用情況

### 監控和告警
- 使用 Prometheus 收集指標
- 配置 Grafana 儀表板
- 設置關鍵指標告警
- 實施健康檢查端點

---

**版本**: v1.0.0
**最後更新**: 2024-01-15
**維護者**: DataSyncHub 開發團隊
